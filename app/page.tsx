"use client"
import { PaymentForm } from "@/components/payment-form"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, Zap, Globe, CheckCircle } from "lucide-react"

export default function HomePage() {
  const router = useRouter()

  const handlePaymentSuccess = (paymentId: string) => {
    router.push(`/payment/success?payment_id=${paymentId}`)
  }

  const handlePaymentError = (error: string) => {
    router.push(`/payment/failure?error=${encodeURIComponent(error)}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">SecurePay</h1>
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span className="text-sm text-gray-600">安全支付</span>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* 左侧：功能介绍 */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-4">安全、快速的在线支付</h2>
              <p className="text-xl text-gray-600">
                使用 Checkout.com 提供的企业级支付解决方案，享受安全可靠的支付体验。
              </p>
            </div>

            <div className="grid gap-6">
              <div className="flex items-start gap-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">银行级安全</h3>
                  <p className="text-gray-600">PCI DSS Level 1 认证，256位SSL加密保护</p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">即时处理</h3>
                  <p className="text-gray-600">实时支付处理，快速确认交易结果</p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">全球支持</h3>
                  <p className="text-gray-600">支持多种货币和国际信用卡</p>
                </div>
              </div>
            </div>

            {/* 支持的卡片类型 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  支持的支付方式
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>• Visa</div>
                  <div>• Mastercard</div>
                  <div>• American Express</div>
                  <div>• Discover</div>
                  <div>• JCB</div>
                  <div>• Diners Club</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：支付表单 */}
          <div>
            <PaymentForm onPaymentSuccess={handlePaymentSuccess} onPaymentError={handlePaymentError} />
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500">
            <p>&copy; 2024 SecurePay. Powered by Checkout.com</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
