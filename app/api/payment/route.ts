import { type NextRequest, NextResponse } from "next/server"
import type { PaymentResponse } from "@/types/payment"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token, amount, currency, reference, customer } = body

    // 调用 Checkout.com API 处理支付
    const paymentResponse = await fetch("https://api.checkout.com/payments", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.CHECKOUT_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        source: {
          type: "token",
          token: token,
        },
        amount: amount,
        currency: currency,
        reference: reference,
        customer: customer,
        capture: true,
        success_url: `${request.nextUrl.origin}/payment/success`,
        failure_url: `${request.nextUrl.origin}/payment/failure`,
      }),
    })

    const paymentResult = await paymentResponse.json()

    if (!paymentResponse.ok) {
      return NextResponse.json(
        {
          error: "Payment failed",
          details: paymentResult,
        },
        { status: 400 },
      )
    }

    // 返回支付结果
    const response: PaymentResponse = {
      id: paymentResult.id,
      status: paymentResult.status,
      amount: paymentResult.amount,
      currency: paymentResult.currency,
      reference: paymentResult.reference,
      approved: paymentResult.approved,
      response_code: paymentResult.response_code,
      response_summary: paymentResult.response_summary,
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Payment processing error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
