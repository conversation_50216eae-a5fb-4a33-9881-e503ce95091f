"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, CreditCard, Shield, Lock } from "lucide-react"
import type { PaymentRequest, FramesCardTokenizedEvent } from "@/types/payment"

declare global {
  interface Window {
    Frames: any
  }
}

interface PaymentFormProps {
  onPaymentSuccess: (paymentId: string) => void
  onPaymentError: (error: string) => void
}

export function PaymentForm({ onPaymentSuccess, onPaymentError }: PaymentFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isFramesReady, setIsFramesReady] = useState(false)
  const [customerInfo, setCustomerInfo] = useState({
    email: "",
    name: "",
    amount: 1000, // 默认 $10.00 (以分为单位)
  })
  const [error, setError] = useState<string | null>(null)
  const framesInitialized = useRef(false)

  useEffect(() => {
    // 加载 Checkout.com Frames SDK
    const script = document.createElement("script")
    script.src = "https://cdn.checkout.com/js/framesv2.min.js"
    script.async = true
    script.onload = initializeFrames
    document.head.appendChild(script)

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script)
      }
    }
  }, [])

  const initializeFrames = () => {
    if (framesInitialized.current || !window.Frames) return

    framesInitialized.current = true

    window.Frames.init({
      publicKey: process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY,
      style: {
        base: {
          fontSize: "16px",
          fontFamily: "system-ui, -apple-system, sans-serif",
          color: "#333",
          "::placeholder": {
            color: "#999",
          },
        },
        invalid: {
          color: "#e53e3e",
        },
        focus: {
          border: "2px solid #3182ce",
        },
      },
      modes: [
        {
          selector: ".card-number-frame",
          type: "card-number",
        },
        {
          selector: ".expiry-date-frame",
          type: "expiry-date",
        },
        {
          selector: ".cvv-frame",
          type: "cvv",
        },
      ],
    })

    // 监听 Frames 事件
    window.Frames.addEventHandler(window.Frames.Events.CARD_VALIDATION_CHANGED, (event: any) => {
      console.log("Card validation changed:", event)
    })

    window.Frames.addEventHandler(window.Frames.Events.CARD_TOKENIZED, handleCardTokenized)

    window.Frames.addEventHandler(window.Frames.Events.CARD_TOKENIZATION_FAILED, (event: any) => {
      setError("卡片验证失败，请检查您的卡片信息")
      setIsLoading(false)
    })

    window.Frames.addEventHandler(window.Frames.Events.READY, () => {
      setIsFramesReady(true)
    })
  }

  const handleCardTokenized = async (event: FramesCardTokenizedEvent) => {
    try {
      const paymentData: PaymentRequest = {
        amount: customerInfo.amount,
        currency: "USD",
        reference: `order-${Date.now()}`,
        customer: {
          email: customerInfo.email,
          name: customerInfo.name,
        },
      }

      const response = await fetch("/api/payment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token: event.token,
          ...paymentData,
        }),
      })

      const result = await response.json()

      if (response.ok && result.approved) {
        onPaymentSuccess(result.id)
      } else {
        onPaymentError(result.response_summary || "支付失败")
      }
    } catch (error) {
      onPaymentError("支付处理过程中发生错误")
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsLoading(true)

    if (!customerInfo.email || !customerInfo.name) {
      setError("请填写所有必填信息")
      setIsLoading(false)
      return
    }

    // 触发卡片令牌化
    window.Frames.submitCard()
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-blue-100 rounded-full">
            <CreditCard className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        <CardTitle className="text-2xl">安全支付</CardTitle>
        <CardDescription>使用 Checkout.com 安全支付系统</CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 客户信息 */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">姓名 *</Label>
              <Input
                id="name"
                type="text"
                value={customerInfo.name}
                onChange={(e) => setCustomerInfo((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="请输入您的姓名"
                required
              />
            </div>

            <div>
              <Label htmlFor="email">邮箱 *</Label>
              <Input
                id="email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => setCustomerInfo((prev) => ({ ...prev, email: e.target.value }))}
                placeholder="请输入您的邮箱"
                required
              />
            </div>

            <div>
              <Label htmlFor="amount">金额 (USD)</Label>
              <Input
                id="amount"
                type="number"
                min="100"
                step="100"
                value={customerInfo.amount}
                onChange={(e) =>
                  setCustomerInfo((prev) => ({ ...prev, amount: Number.parseInt(e.target.value) || 1000 }))
                }
                placeholder="1000"
              />
              <p className="text-sm text-gray-500 mt-1">金额以分为单位 (1000 = $10.00)</p>
            </div>
          </div>

          {/* 卡片信息 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span className="text-sm text-gray-600">您的卡片信息经过加密保护</span>
            </div>

            <div>
              <Label>卡号 *</Label>
              <div className="card-number-frame border rounded-md p-3 min-h-[48px] bg-white"></div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>有效期 *</Label>
                <div className="expiry-date-frame border rounded-md p-3 min-h-[48px] bg-white"></div>
              </div>
              <div>
                <Label>CVV *</Label>
                <div className="cvv-frame border rounded-md p-3 min-h-[48px] bg-white"></div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Lock className="h-4 w-4" />
            <span>256位SSL加密保护</span>
          </div>
        </CardContent>

        <CardFooter>
          <Button type="submit" className="w-full" disabled={isLoading || !isFramesReady}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                处理中...
              </>
            ) : (
              `支付 $${(customerInfo.amount / 100).toFixed(2)}`
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
