export interface PaymentRequest {
  amount: number
  currency: string
  reference: string
  customer: {
    email: string
    name: string
  }
}

export interface PaymentResponse {
  id: string
  status: string
  amount: number
  currency: string
  reference: string
  approved: boolean
  response_code: string
  response_summary: string
}

export interface FramesCardTokenizedEvent {
  type: "CARD_TOKENIZED"
  token: string
  scheme: string
  last4: string
  bin: string
  card_type: string
  issuer_country: string
  product_id: string
  product_type: string
}
